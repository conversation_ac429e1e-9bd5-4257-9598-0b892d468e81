"use client"

import type React from "react"

import { useState, use<PERSON>em<PERSON>, useEffect } from "react"
import { Search, Plus, Edit, Trash2, Building, MapPin, Phone, Mail, User, AlertTriangle, Loader2 } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialog<PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog"
import { toast } from "@/hooks/use-toast"
import { LpkMitraService, type LpkMitra } from "@/lib/services"

interface FormData {
  nama_lpk: string
  alamat_lengkap: string
  kota: string
  provinsi: string
  nama_pimpinan: string
  kontak_person: string
  nomor_telepon: string
  email: string
  website?: string
  tanggal_kerjasama: string
  catatan?: string
}

interface FormErrors {
  nama_lpk?: string
  alamat_lengkap?: string
  kota?: string
  provinsi?: string
  nama_pimpinan?: string
  kontak_person?: string
  nomor_telepon?: string
  email?: string
  tanggal_kerjasama?: string
}

export default function LpkMitraPage() {
  const [lpkData, setLpkData] = useState<LpkMitra[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [editingLpk, setEditingLpk] = useState<LpkMitra | null>(null)
  const [deletingLpk, setDeletingLpk] = useState<LpkMitra | null>(null)
  const [formData, setFormData] = useState<FormData>({
    nama_lpk: "",
    alamat_lengkap: "",
    kota: "",
    provinsi: "",
    nama_pimpinan: "",
    kontak_person: "",
    nomor_telepon: "",
    email: "",
    website: "",
    tanggal_kerjasama: "",
    catatan: "",
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fetch LPK data
  useEffect(() => {
    const fetchLpkData = async () => {
      try {
        setLoading(true)
        setError(null)
        const data = await LpkMitraService.getWithStudentCount()
        setLpkData(data)
      } catch (err) {
        console.error('Error fetching LPK data:', err)
        setError('Gagal memuat data LPK Mitra. Silakan refresh halaman.')
      } finally {
        setLoading(false)
      }
    }

    fetchLpkData()
  }, [])

  // Filter data based on search term
  const filteredData = useMemo(() => {
    if (!searchTerm) return lpkData

    return lpkData.filter(
      (lpk) =>
        lpk.nama_lpk.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lpk.kota.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lpk.kontak_person.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lpk.nama_pimpinan.toLowerCase().includes(searchTerm.toLowerCase()),
    )
  }, [lpkData, searchTerm])

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalLpk = lpkData.length
    const totalSiswa = lpkData.reduce((sum, lpk) => {
      // Get student count from the siswa relation if available
      const studentCount = (lpk as any).siswa?.length || 0
      return sum + studentCount
    }, 0)
    const rataRataSiswa = totalLpk > 0 ? Math.round(totalSiswa / totalLpk) : 0
    const lpkAktif = lpkData.filter((lpk) => lpk.status === "aktif").length

    return {
      totalLpk,
      totalSiswa,
      rataRataSiswa,
      lpkAktif,
    }
  }, [lpkData])

  // Validate form
  const validateForm = (data: FormData): FormErrors => {
    const errors: FormErrors = {}

    if (!data.nama_lpk.trim()) {
      errors.nama_lpk = "Nama LPK wajib diisi"
    } else if (data.nama_lpk.trim().length < 3) {
      errors.nama_lpk = "Nama LPK minimal 3 karakter"
    }

    if (!data.alamat_lengkap.trim()) {
      errors.alamat_lengkap = "Alamat lengkap wajib diisi"
    }

    if (!data.kota.trim()) {
      errors.kota = "Kota wajib diisi"
    }

    if (!data.provinsi.trim()) {
      errors.provinsi = "Provinsi wajib diisi"
    }

    if (!data.nama_pimpinan.trim()) {
      errors.nama_pimpinan = "Nama pimpinan wajib diisi"
    }

    if (!data.kontak_person.trim()) {
      errors.kontak_person = "Kontak person wajib diisi"
    }

    if (!data.nomor_telepon.trim()) {
      errors.nomor_telepon = "Nomor telepon wajib diisi"
    } else if (!/^[0-9\-+().\s]+$/.test(data.nomor_telepon)) {
      errors.nomor_telepon = "Format nomor telepon tidak valid"
    }

    if (!data.tanggal_kerjasama.trim()) {
      errors.tanggal_kerjasama = "Tanggal kerjasama wajib diisi"
    }

    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.email = "Format email tidak valid"
    }

    return errors
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    const errors = validateForm(formData)
    setFormErrors(errors)

    if (Object.keys(errors).length > 0) {
      setIsSubmitting(false)
      return
    }

    try {
      const lpkPayload = {
        ...formData,
        status: 'aktif' as const,
      }

      if (editingLpk) {
        // Update existing LPK
        const updatedLpk = await LpkMitraService.update(editingLpk.id, lpkPayload)
        setLpkData((prev) =>
          prev.map((lpk) =>
            lpk.id === editingLpk.id ? updatedLpk : lpk
          )
        )
        toast({
          title: "Berhasil",
          description: "Data LPK Mitra berhasil diperbarui",
        })
      } else {
        // Add new LPK
        const newLpk = await LpkMitraService.create(lpkPayload)
        setLpkData((prev) => [...prev, newLpk])
        toast({
          title: "Berhasil",
          description: "LPK Mitra baru berhasil ditambahkan",
        })
      }

      handleCloseModal()
    } catch (error) {
      console.error('Error saving LPK:', error)
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat menyimpan data",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle delete
  const handleDelete = async () => {
    if (!deletingLpk) return

    try {
      await LpkMitraService.delete(deletingLpk.id)
      setLpkData((prev) => prev.filter((lpk) => lpk.id !== deletingLpk.id))
      toast({
        title: "Berhasil",
        description: `LPK ${deletingLpk.nama_lpk} berhasil dihapus`,
      })
      setIsDeleteDialogOpen(false)
      setDeletingLpk(null)
    } catch (error) {
      console.error('Error deleting LPK:', error)
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat menghapus data",
        variant: "destructive",
      })
    }
  }

  // Handle modal open/close
  const handleOpenModal = (lpk?: LpkMitra) => {
    if (lpk) {
      setEditingLpk(lpk)
      setFormData({
        nama_lpk: lpk.nama_lpk,
        alamat_lengkap: lpk.alamat_lengkap,
        kota: lpk.kota,
        provinsi: lpk.provinsi,
        nama_pimpinan: lpk.nama_pimpinan,
        kontak_person: lpk.kontak_person,
        nomor_telepon: lpk.nomor_telepon,
        email: lpk.email,
        website: lpk.website || "",
        tanggal_kerjasama: lpk.tanggal_kerjasama.split('T')[0], // Format for date input
        catatan: lpk.catatan || "",
      })
    } else {
      setEditingLpk(null)
      setFormData({
        nama_lpk: "",
        alamat_lengkap: "",
        kota: "",
        provinsi: "",
        nama_pimpinan: "",
        kontak_person: "",
        nomor_telepon: "",
        email: "",
        website: "",
        tanggal_kerjasama: "",
        catatan: "",
      })
    }
    setFormErrors({})
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingLpk(null)
    setFormData({
      nama_lpk: "",
      alamat_lengkap: "",
      kota: "",
      provinsi: "",
      nama_pimpinan: "",
      kontak_person: "",
      nomor_telepon: "",
      email: "",
      website: "",
      tanggal_kerjasama: "",
      catatan: "",
    })
    setFormErrors({})
  }

  // Handle delete dialog
  const handleOpenDeleteDialog = (lpk: LpkMitra) => {
    setDeletingLpk(lpk)
    setIsDeleteDialogOpen(true)
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-orange-500" />
          <p className="text-gray-600">Memuat data LPK Mitra...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">Terjadi Kesalahan</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              className="bg-red-500 hover:bg-red-600"
            >
              Refresh Halaman
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Data Master LPK Mitra</h1>
          <p className="text-gray-600 mt-1">Kelola data lembaga pelatihan kerja mitra program magang Jepang</p>
        </div>
        <Button onClick={() => handleOpenModal()} className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Tambah LPK Mitra
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total LPK Mitra</CardTitle>
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg">
              <Building className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.totalLpk}</div>
            <p className="text-xs text-blue-600 flex items-center mt-1">Lembaga terdaftar</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">LPK Aktif</CardTitle>
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg">
              <Building className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.lpkAktif}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">Sedang beroperasi</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Siswa</CardTitle>
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-2 rounded-lg">
              <User className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.totalSiswa}</div>
            <p className="text-xs text-orange-600 flex items-center mt-1">Dari semua LPK</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-maroon-600">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Rata-rata Siswa</CardTitle>
            <div className="bg-gradient-to-r from-maroon-600 to-maroon-700 p-2 rounded-lg">
              <User className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.rataRataSiswa}</div>
            <p className="text-xs text-maroon-600 flex items-center mt-1">Per LPK</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Table */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Daftar LPK Mitra</CardTitle>
              <CardDescription>Kelola data lembaga pelatihan kerja mitra</CardDescription>
            </div>
            <div className="relative w-full sm:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Cari berdasarkan nama LPK atau kota..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-center w-16">No.</TableHead>
                  <TableHead className="font-semibold">Nama LPK</TableHead>
                  <TableHead className="font-semibold">Alamat</TableHead>
                  <TableHead className="font-semibold">Kota/Kabupaten</TableHead>
                  <TableHead className="font-semibold">Kontak/No. HP</TableHead>
                  <TableHead className="font-semibold">Email</TableHead>
                  <TableHead className="font-semibold text-center">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length > 0 ? (
                  filteredData.map((lpk, index) => (
                    <TableRow key={lpk.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">{lpk.nama_lpk}</div>
                          {lpk.catatan && (
                            <div className="text-xs text-gray-500 mt-1 max-w-xs truncate">{lpk.catatan}</div>
                          )}
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {(lpk as any).siswa?.length || 0} siswa
                            </Badge>
                            <Badge
                              variant="outline"
                              className={
                                lpk.status === "aktif"
                                  ? "bg-green-100 text-green-800 border-green-200"
                                  : "bg-gray-100 text-gray-800 border-gray-200"
                              }
                            >
                              {lpk.status === "aktif" ? "Aktif" : "Nonaktif"}
                            </Badge>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-start gap-2">
                          <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{lpk.alamat_lengkap}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium text-gray-900">{lpk.kota}</div>
                        <div className="text-xs text-gray-500">{lpk.provinsi}</div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-700">{lpk.kontak_person}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-700">{lpk.nomor_telepon}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {lpk.email ? (
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-700">{lpk.email}</span>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400 italic">Tidak ada email</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOpenModal(lpk)}
                            className="hover:bg-blue-50 hover:border-blue-300"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOpenDeleteDialog(lpk)}
                            className="hover:bg-red-50 hover:border-red-300 text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12">
                      <div className="flex flex-col items-center gap-2">
                        <Building className="h-12 w-12 text-gray-400" />
                        <h3 className="text-lg font-medium text-gray-900">Tidak ada data LPK</h3>
                        <p className="text-gray-500">
                          {searchTerm
                            ? "Tidak ditemukan LPK yang sesuai dengan pencarian"
                            : "Belum ada LPK yang terdaftar"}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Table Footer */}
          {filteredData.length > 0 && (
            <div className="px-6 py-4 border-t bg-gray-50">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  Menampilkan {filteredData.length} dari {lpkData.length} LPK Mitra
                </span>
                {searchTerm && <span className="text-blue-600">Hasil pencarian untuk "{searchTerm}"</span>}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-gray-900">
              {editingLpk ? "Edit LPK Mitra" : "Tambah LPK Mitra Baru"}
            </DialogTitle>
            <DialogDescription>
              {editingLpk
                ? "Perbarui informasi LPK Mitra yang sudah ada"
                : "Masukkan informasi LPK Mitra baru untuk didaftarkan ke sistem"}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Nama LPK */}
              <div className="md:col-span-2">
                <Label htmlFor="namaLpk" className="text-sm font-medium text-gray-700">
                  Nama LPK Mitra <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="namaLpk"
                  value={formData.namaLpk}
                  onChange={(e) => setFormData({ ...formData, namaLpk: e.target.value })}
                  placeholder="Masukkan nama lengkap LPK"
                  className={formErrors.namaLpk ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.namaLpk && <p className="text-sm text-red-600 mt-1">{formErrors.namaLpk}</p>}
              </div>

              {/* Alamat */}
              <div className="md:col-span-2">
                <Label htmlFor="alamat" className="text-sm font-medium text-gray-700">
                  Alamat Lengkap <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="alamat"
                  value={formData.alamat}
                  onChange={(e) => setFormData({ ...formData, alamat: e.target.value })}
                  placeholder="Masukkan alamat lengkap LPK"
                  className={formErrors.alamat ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.alamat && <p className="text-sm text-red-600 mt-1">{formErrors.alamat}</p>}
              </div>

              {/* Kota */}
              <div>
                <Label htmlFor="kota" className="text-sm font-medium text-gray-700">
                  Kota/Kabupaten <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="kota"
                  value={formData.kota}
                  onChange={(e) => setFormData({ ...formData, kota: e.target.value })}
                  placeholder="Masukkan kota/kabupaten"
                  className={formErrors.kota ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.kota && <p className="text-sm text-red-600 mt-1">{formErrors.kota}</p>}
              </div>

              {/* Kontak Person */}
              <div>
                <Label htmlFor="kontakPerson" className="text-sm font-medium text-gray-700">
                  Kontak Person <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="kontakPerson"
                  value={formData.kontakPerson}
                  onChange={(e) => setFormData({ ...formData, kontakPerson: e.target.value })}
                  placeholder="Nama penanggung jawab"
                  className={formErrors.kontakPerson ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.kontakPerson && <p className="text-sm text-red-600 mt-1">{formErrors.kontakPerson}</p>}
              </div>

              {/* Nomor Telepon */}
              <div>
                <Label htmlFor="nomorTelepon" className="text-sm font-medium text-gray-700">
                  Nomor Telepon <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="nomorTelepon"
                  value={formData.nomorTelepon}
                  onChange={(e) => setFormData({ ...formData, nomorTelepon: e.target.value })}
                  placeholder="Contoh: 021-1234567"
                  className={formErrors.nomorTelepon ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.nomorTelepon && <p className="text-sm text-red-600 mt-1">{formErrors.nomorTelepon}</p>}
              </div>

              {/* Email */}
              <div>
                <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Email (Opsional)
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  placeholder="<EMAIL>"
                  className={formErrors.email ? "border-red-500 focus:border-red-500" : ""}
                />
                {formErrors.email && <p className="text-sm text-red-600 mt-1">{formErrors.email}</p>}
              </div>

              {/* Catatan */}
              <div className="md:col-span-2">
                <Label htmlFor="catatan" className="text-sm font-medium text-gray-700">
                  Catatan/Keterangan (Opsional)
                </Label>
                <Textarea
                  id="catatan"
                  value={formData.catatan}
                  onChange={(e) => setFormData({ ...formData, catatan: e.target.value })}
                  placeholder="Masukkan catatan atau keterangan tambahan tentang LPK"
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter className="gap-2">
              <Button type="button" variant="outline" onClick={handleCloseModal} disabled={isSubmitting}>
                Batal
              </Button>
              <Button type="submit" disabled={isSubmitting} className="bg-orange-500 hover:bg-orange-600 text-white">
                {isSubmitting ? "Menyimpan..." : editingLpk ? "Perbarui" : "Simpan"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Konfirmasi Hapus
            </AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus LPK Mitra{" "}
              <span className="font-semibold text-gray-900">"{deletingLpk?.nama_lpk}"</span>?
              <br />
              <br />
              Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data yang terkait dengan LPK ini.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700 text-white">
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
