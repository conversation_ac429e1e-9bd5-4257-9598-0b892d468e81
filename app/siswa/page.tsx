"use client"

import { useState, use<PERSON>emo, useEffect } from "react"
import {
  Search,
  Plus,
  Eye,
  Edit,
  Trash2,
  Filter,
  Users,
  AlertTriangle,
  Loader2,
  <PERSON>r<PERSON><PERSON><PERSON>,
  <PERSON>r<PERSON>,
  Clock,
} from "lucide-react"
import Link from "next/link"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "@/hooks/use-toast"
import { SiswaService, type Siswa } from "@/lib/services"

export default function SiswaPage() {
  const [siswaData, setSiswaData] = useState<Siswa[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("")
  const [genderFilter, setGenderFilter] = useState<string>("")
  const [educationFilter, setEducationFilter] = useState<string>("")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deletingSiswa, setDeletingSiswa] = useState<Siswa | null>(null)

  // Fetch Siswa data
  useEffect(() => {
    const fetchSiswaData = async () => {
      try {
        setLoading(true)
        setError(null)
        const data = await SiswaService.getAll()
        setSiswaData(data)
      } catch (err) {
        console.error('Error fetching Siswa data:', err)
        setError('Gagal memuat data Siswa. Silakan refresh halaman.')
      } finally {
        setLoading(false)
      }
    }

    fetchSiswaData()
  }, [])

  // Filter data based on search term and filters
  const filteredData = useMemo(() => {
    let filtered = siswaData

    if (searchTerm) {
      filtered = filtered.filter(
        (siswa) =>
          siswa.nama_lengkap.toLowerCase().includes(searchTerm.toLowerCase()) ||
          siswa.nik.toLowerCase().includes(searchTerm.toLowerCase()) ||
          siswa.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (siswa as any).lpk_mitra?.nama_lpk.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter) {
      filtered = filtered.filter((siswa) => siswa.status_pendaftaran === statusFilter)
    }

    if (genderFilter) {
      filtered = filtered.filter((siswa) => siswa.jenis_kelamin === genderFilter)
    }

    if (educationFilter) {
      filtered = filtered.filter((siswa) => siswa.pendidikan_terakhir === educationFilter)
    }

    return filtered
  }, [siswaData, searchTerm, statusFilter, genderFilter, educationFilter])

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalSiswa = siswaData.length
    const approved = siswaData.filter((siswa) => siswa.status_pendaftaran === "approved").length
    const pending = siswaData.filter((siswa) => 
      ["draft", "submitted", "review"].includes(siswa.status_pendaftaran)
    ).length
    const rejected = siswaData.filter((siswa) => siswa.status_pendaftaran === "rejected").length
    const placed = siswaData.filter((siswa) => 
      (siswa as any).penempatan_siswa?.some((p: any) => 
        ["ditempatkan", "berangkat", "aktif"].includes(p.status_penempatan)
      )
    ).length

    return {
      totalSiswa,
      approved,
      pending,
      rejected,
      placed,
      available: approved - placed,
    }
  }, [siswaData])

  // Handle delete
  const handleDelete = async () => {
    if (!deletingSiswa) return

    try {
      await SiswaService.delete(deletingSiswa.id)
      setSiswaData((prev) => prev.filter((siswa) => siswa.id !== deletingSiswa.id))
      toast({
        title: "Berhasil",
        description: `Data siswa ${deletingSiswa.nama_lengkap} berhasil dihapus`,
      })
      setIsDeleteDialogOpen(false)
      setDeletingSiswa(null)
    } catch (error) {
      console.error('Error deleting Siswa:', error)
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat menghapus data",
        variant: "destructive",
      })
    }
  }

  // Handle delete dialog
  const handleOpenDeleteDialog = (siswa: Siswa) => {
    setDeletingSiswa(siswa)
    setIsDeleteDialogOpen(true)
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Disetujui</Badge>
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Ditolak</Badge>
      case 'review':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Review</Badge>
      case 'submitted':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Disubmit</Badge>
      case 'draft':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Draft</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Get placement status
  const getPlacementStatus = (siswa: any) => {
    const placements = siswa.penempatan_siswa || []
    const activePlacement = placements.find((p: any) => 
      ["ditempatkan", "berangkat", "aktif"].includes(p.status_penempatan)
    )
    
    if (activePlacement) {
      return (
        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
          {activePlacement.status_penempatan === 'aktif' ? 'Aktif di Jepang' : 
           activePlacement.status_penempatan === 'berangkat' ? 'Sudah Berangkat' : 'Ditempatkan'}
        </Badge>
      )
    }
    
    return siswa.status_pendaftaran === 'approved' ? (
      <Badge className="bg-green-100 text-green-800 border-green-200">Tersedia</Badge>
    ) : null
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-orange-500" />
          <p className="text-gray-600">Memuat data Siswa...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">Terjadi Kesalahan</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <Button 
              onClick={() => window.location.reload()} 
              className="bg-red-500 hover:bg-red-600"
            >
              Refresh Halaman
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Data Master Siswa</h1>
          <p className="text-gray-600 mt-1">Kelola data siswa peserta program magang Jepang</p>
        </div>
        <Button className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Tambah Siswa
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Siswa</CardTitle>
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg">
              <Users className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.totalSiswa}</div>
            <p className="text-xs text-blue-600 flex items-center mt-1">Siswa terdaftar</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Siswa Disetujui</CardTitle>
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg">
              <UserCheck className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.approved}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">Siap ditempatkan</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Menunggu Review</CardTitle>
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-2 rounded-lg">
              <Clock className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.pending}</div>
            <p className="text-xs text-orange-600 flex items-center mt-1">Perlu review</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-maroon-600">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Sudah Ditempatkan</CardTitle>
            <div className="bg-gradient-to-r from-maroon-600 to-maroon-700 p-2 rounded-lg">
              <UserCheck className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{statistics.placed}</div>
            <p className="text-xs text-maroon-600 flex items-center mt-1">Aktif bekerja</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Daftar Siswa</CardTitle>
              <CardDescription>Kelola data siswa peserta program magang</CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
              <div className="relative w-full sm:w-80">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari berdasarkan nama atau NIK..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Status</SelectItem>
                    <SelectItem value="approved">Disetujui</SelectItem>
                    <SelectItem value="review">Review</SelectItem>
                    <SelectItem value="submitted">Disubmit</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="rejected">Ditolak</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={genderFilter} onValueChange={setGenderFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua</SelectItem>
                    <SelectItem value="L">Laki-laki</SelectItem>
                    <SelectItem value="P">Perempuan</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-center w-16">No.</TableHead>
                  <TableHead className="font-semibold">Siswa</TableHead>
                  <TableHead className="font-semibold">LPK Asal</TableHead>
                  <TableHead className="font-semibold">Pendidikan</TableHead>
                  <TableHead className="font-semibold">Status Pendaftaran</TableHead>
                  <TableHead className="font-semibold">Status Penempatan</TableHead>
                  <TableHead className="font-semibold text-center">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length > 0 ? (
                  filteredData.map((siswa, index) => (
                    <TableRow key={siswa.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">{siswa.nama_lengkap}</div>
                          <div className="text-sm text-gray-500">NIK: {siswa.nik}</div>
                          <div className="text-xs text-gray-400 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {siswa.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan'}
                            </Badge>
                            <span className="ml-2 text-gray-500">
                              {new Date().getFullYear() - new Date(siswa.tanggal_lahir).getFullYear()} tahun
                            </span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-gray-700">
                          {(siswa as any).lpk_mitra?.nama_lpk || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">{siswa.pendidikan_terakhir}</div>
                          {siswa.jurusan && (
                            <div className="text-xs text-gray-500">{siswa.jurusan}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(siswa.status_pendaftaran)}
                      </TableCell>
                      <TableCell>
                        {getPlacementStatus(siswa) || (
                          <span className="text-xs text-gray-400">Belum ditempatkan</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center gap-2">
                          <Link href={`/siswa/${siswa.id}`}>
                            <Button
                              variant="outline"
                              size="sm"
                              className="hover:bg-blue-50 hover:border-blue-300"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="outline"
                            size="sm"
                            className="hover:bg-green-50 hover:border-green-300"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOpenDeleteDialog(siswa)}
                            className="hover:bg-red-50 hover:border-red-300 text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12">
                      <div className="flex flex-col items-center gap-2">
                        <Users className="h-12 w-12 text-gray-400" />
                        <h3 className="text-lg font-medium text-gray-900">Tidak ada data Siswa</h3>
                        <p className="text-gray-500">
                          {searchTerm || statusFilter || genderFilter
                            ? "Tidak ditemukan Siswa yang sesuai dengan filter"
                            : "Belum ada Siswa yang terdaftar"}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Table Footer */}
          {filteredData.length > 0 && (
            <div className="px-6 py-4 border-t bg-gray-50">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  Menampilkan {filteredData.length} dari {siswaData.length} Siswa
                </span>
                {(searchTerm || statusFilter || genderFilter) && (
                  <span className="text-blue-600">Filter aktif</span>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Konfirmasi Hapus
            </AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus data siswa{" "}
              <span className="font-semibold text-gray-900">"{deletingSiswa?.nama_lengkap}"</span>?
              <br />
              <br />
              Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data yang terkait dengan siswa ini.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700 text-white">
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
